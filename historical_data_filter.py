import copy
import json
import pymysql
import redis
import unicodedata
from unidecode import unidecode
from dbutils.pooled_db import PooledDB
from setting import *
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed

# 读取数据
info = dict(
    blocking=True,  # 连接池中如果没有可用连接后，是否阻塞等待
    ping=1,  # 在每次获取连接时ping
    mincached=1,  # 初始化时，连接池中至少创建的空闲的连接，0表示不创建
    maxcached=5,  # 连接池中最多闲置的连接，0和None不限制
    maxconnections=20,  # 连接池允许的最大连接数，0和None表示不限制连接数
)
# target
info_target = copy.deepcopy(info)
info_target.update(MYSQL_INFO_TARGET)
pool = PooledDB(creator=pymysql, **info_target)


def _read_data(sql, retry_num=3):
    for _ in range(retry_num):
        try:
            with pool.connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(sql)
                    data_raw = cur.fetchall()
            return data_raw if data_raw else tuple()
        except Exception as e:
            exc_type, exc_obj, exc_tb = sys.exc_info()
            logger.warning(f"{exc_type.__name__}: {e}")
    else:
        logger.error({"msg": "最大重试次数", "sql": sql})


def _write_data(sql, insert_list, retry_num=3):
    for _ in range(retry_num):
        try:
            with pool.connection() as conn:
                with conn.cursor() as cur:
                    cur.executemany(sql, insert_list)
                conn.commit()
            return 1
        except Exception as e:
            exc_type, exc_obj, exc_tb = sys.exc_info()
            logger.warning(f"{exc_type.__name__}: {e}")
    else:
        logger.error({"msg": "最大重试次数", "sql": sql})


def execute_one(sql, retry_num=3):
    for _ in range(retry_num):
        try:
            with pool.connection() as conn:
                with conn.cursor() as cur:
                    cur.execute(sql)
                conn.commit()
            return 1
        except Exception as e:
            exc_type, exc_obj, exc_tb = sys.exc_info()
            logger.warning(f"{exc_type.__name__}: {e}")
    else:
        logger.error({"msg": "最大重试次数", "sql": sql})


# def company():
#     sql = "select min(company_id) min_id, max(company_id) max_id from t_company"
#     data = _read_data(sql)
#     min_id, max_id = data[0]
#     logger.info({"min_id": min_id, "max_id": max_id})
#     batch_size = 1000
#     for i in range(int(min_id), int(max_id) + 1, batch_size):
#         start = i
#         end = i + batch_size - 1
#         logger.info({"start": start, "end": end})
#         sql = f"select source, openid from t_company where company_id between {start} and {end}"
#         data = _read_data(sql)
#         logger.info({"data_length": len(data)})
#
#         pipe = red.pipeline()
#         for d in data:
#             source, openid = d
#             if source == 1:
#                 pipe.sadd(REDIS_HAIGUAN_DATA_WMB_COMPANY, openid)
#             elif source == 4:
#                 openid = normalize_string(openid).upper()
#                 pipe.sadd(REDIS_HAIGUAN_DATA_DINGYI_COMPANY, openid)
#         pipe.execute()


def company_to_mysql():
    # mapping = _mapping_country()

    sql = "select min(company_id) min_id, max(company_id) max_id from t_company"
    # sql = "select min(company_id) min_id, max(company_id) max_id from t_company where source = 4"
    data = _read_data(sql)
    min_id, max_id = data[0]
    logger.info({"min_id": min_id, "max_id": max_id})
    batch_size = 100000
    for i in range(int(min_id), int(max_id) + 1, batch_size):
        start = i
        end = i + batch_size - 1
        logger.info({"start": start, "end": end})
        sql = f"select source, openid, country_id from t_company where company_id between {start} and {end}"
        data = _read_data(sql)
        logger.info({"data_length": len(data)})

        cur_id_set_wmb = set()
        cur_id_set_dingyi = set()
        for d in data:
            source, openid, country_id = d
            if source == 1:
                cur_id_set_wmb.add(openid)
            elif source == 4:
                openid = openid.upper()
                uniqueid = f"{openid}-{country_id}"
                cur_id_set_dingyi.add(uniqueid)
        if len(cur_id_set_wmb) > 0:
            insert_company('t_company_open_wmb', cur_id_set_wmb)
        if len(cur_id_set_dingyi) > 0:
            insert_company('t_company_open_dingyi', cur_id_set_dingyi)


def insert_company(table, insert_set):
    sql = f"insert into {table}(uniqueid) value(%s) on duplicate key update uniqueid=values(uniqueid)"
    logger.info({"table": table, "length": len(insert_set)})
    _write_data(sql, [[x, ] for x in insert_set])


def trade():
    # 读取数据
    sql = "select min(id) min_id, max(id) max_id from t_trade_open"
    data = _read_data(sql)
    min_id, max_id = data[0]
    logger.info({"min_id": min_id, "max_id": max_id})
    for i in range(int(min_id), int(max_id) + 1, BATCH_SIZE):
        start = i
        end = i + BATCH_SIZE - 1
        sql = f"select source, openid from t_trade_open where id between {start} and {end}"
        logger.info({"start": start, "end": end})
        data = _read_data(sql)
        sql_insert_wmb = f"insert into t_trade_open_wmb(openid) value(%s) on duplicate key update openid=values(openid)"
        insert_list_wmb = list()
        sql_insert_dingyi = f"insert into t_trade_open_dingyi(openid) value(%s) on duplicate key update openid=values(openid)"
        insert_list_dingyi = list()
        for d in data:
            source, openid = d
            if source == 1:
                insert_list_wmb.append([openid, ])
            elif source == 4:
                insert_list_dingyi.append([openid, ])

        if len(insert_list_wmb) > 0:
            _write_data(sql_insert_wmb, insert_list_wmb)
        if len(insert_list_dingyi) > 0:
            _write_data(sql_insert_dingyi, insert_list_wmb)


def trade1(start, end):
    sql = f"select source, openid from t_trade where trade_id between {start} and {end}"
    logger.info({"start": start, "end": end})
    data = _read_data(sql)
    sql_insert_wmb = f"insert into t_trade_open_wmb(openid) value(%s) on duplicate key update openid=values(openid)"
    insert_list_wmb = list()
    sql_insert_dingyi = f"insert into t_trade_open_dingyi(openid) value(%s) on duplicate key update openid=values(openid)"
    insert_list_dingyi = list()
    for d in data:
        source, openid = d
        if source == 1:
            insert_list_wmb.append([openid, ])
        elif source == 4:
            insert_list_dingyi.append([openid, ])

    if len(insert_list_wmb) > 0:
        _write_data(sql_insert_wmb, insert_list_wmb)
    if len(insert_list_dingyi) > 0:
        _write_data(sql_insert_dingyi, insert_list_wmb)


def trade1_main():
    # 读取数据
    sql = "select min(trade_id) min_id, max(trade_id) max_id from t_trade"
    data = _read_data(sql)
    min_id, max_id = data[0]
    logger.info({"min_id": min_id, "max_id": max_id})
    if int(min_id) >= int(max_id):
        logger.warning({"table": "t_trade", "msg": "没有新数据"})
        return
    batch_size = 100000
    with ThreadPoolExecutor(max_workers=2) as executor:
        futures = []
        for i in range(int(min_id), int(max_id) + 1, batch_size):
            start = i
            end = i + batch_size - 1
            future = executor.submit(trade1, start, end)
            futures.append(future)

        # 获取每个任务的结果（可选）
        # total = len(futures)
        # c = 0
        for future in as_completed(futures):
            try:
                result = future.result()
                pass
                # print(result)
                # 处理结果（如果有需要的话）
            except Exception as e:
                # 处理异常（如果有需要的话）
                logger.error(e)
                pass
            # c += 1
            # b.run(c, total)


def back_company():
    source = 't_company'
    target = 't_company_back'
    # 复制表
    sql = f"create table if not exists {target} like {source}"
    execute_one(sql)
    # 读取最大最小id
    sql = f"select min(company_id) min_id, max(company_id) max_id from {source}"
    data_raw = _read_data(sql)
    min_id, max_id = data_raw[0]
    logger.info({"min_id": min_id, "max_id": max_id})
    # 分批复制
    for i in range(min_id, max_id + 1, BATCH_SIZE):
        start = i
        end = i + BATCH_SIZE - 1
        sql = f"insert into {target} select * from {source} where company_id between {start} and {end}"
        logger.info({"target": target, "start": start, "end": end})
        # print(sql)
        execute_one(sql)


def back_trade():
    source = 't_trade'
    target = 't_trade_back'
    # 复制表
    sql = f"create table if not exists {target} like {source}"
    execute_one(sql)
    # 读取最大最小id
    sql = f"select min(trade_id) min_id, max(trade_id) max_id from {source}"
    data_raw = _read_data(sql)
    min_id, max_id = data_raw[0]
    logger.info({"min_id": min_id, "max_id": max_id})
    # 分批复制
    for i in range(min_id, max_id + 1, BATCH_SIZE):
        start = i
        end = i + BATCH_SIZE - 1
        sql = f"insert into {target} select * from {source} where trade_id between {start} and {end}"
        logger.info({"target": target, "start": start, "end": end})
        execute_one(sql)


def _mapping_country():
    # 读取c_area_country到内存
    sql = "select id, name_en, name_alias, code from db_customs.c_area_country"
    data_raw = _read_data(sql)
    mapping = dict()
    for data in data_raw:
        _id, name_en, name_alias, code = data
        standard = {"id": _id, "name_en": name_en}
        mapping[name_en.lower()] = standard
        if len(name_alias) > 0:
            for a in json.loads(name_alias):
                mapping[a.lower()] = standard
        mapping[code.lower()] = standard
    return mapping


def back_trade_open():
    source = 't_trade_open_wmb'
    target = 't_trade_open_wmb_back'
    # 复制表
    sql = f"create table if not exists {target} like {source}"
    execute_one(sql)
    # 读取最大最小id
    sql = f"select min(id) min_id, max(id) max_id from {source}"
    data_raw = _read_data(sql)
    min_id, max_id = data_raw[0]
    logger.info({"min_id": min_id, "max_id": max_id})
    # 分批复制
    batch_size = 100000
    for i in range(min_id, max_id + 1, batch_size):
        start = i
        end = i + batch_size - 1
        sql = f"insert into {target} select * from {source} where id between {start} and {end}"
        logger.info({"target": target, "start": start, "end": end})
        execute_one(sql)



"""
cd /root/peng_file/data_processing/wmb && nohup python3 -u historical_data_filter.py > ./logs/historical_data_filter.log 2>&1 &
tail logs/historical_data_filter.log -f
kill -9 $(ps -ef | grep historical_data_filter.py | grep -v grep | awk '{print $2}')
"""

if __name__ == '__main__':
    # company()

    company_to_mysql()

    # trade()
    # trade1_main()

    # back_company()
    # back_trade()

    # back_trade_open()
