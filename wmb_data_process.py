import re
import json
import time

import pytz
import hashlib
from common.database import PoolMysql, DBPoolMysql
from common.utils import statistics
from monitor import send_text_message
from setting import *


class WMB:
    source = 1  # 1: wmb
    shanghai_tz = pytz.timezone('Asia/Shanghai')

    def __init__(self):
        # source(新表数据库连接池)
        self.pool_source = PoolMysql(**MYSQL_INFO_SOURCE)
        self.db_source = DBPoolMysql(self.pool_source)
        # target(旧表数据库连接池)
        self.pool_target = PoolMysql(**MYSQL_INFO_TARGET)
        self.db_target = DBPoolMysql(self.pool_target)
        # mapping
        self.mapping_country = self._mapping_country()
        # table
        self.table_source_record = 't_routine_config'
        self.table_source_company = 'company'
        self.table_source_trade = 'trade'

        # 正式
        self.table_target_company = 't_company'
        self.table_target_trade = 't_trade'
        self.table_exists_company = 't_company_open_wmb'
        self.table_exists_trade = 't_trade_open_wmb'
        self.table_trade_uuid = 't_trade_uuid'
        self.table_port_map = "haiguan_port"

        self.regex_email = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.regex_phone = re.compile(
            r'(?:\+?\d{1,3}[-\s.]?)?(?:\(?\d{1,4}\)?[-\s.]?)?\d{1,4}[-\s.]?\d{1,4}(?:[-\s.]\d{1,9})?')
        self.regex_split_hs = re.compile(r'[, ]+')
        self.regex = re.compile('[^A-Z]')

        self.black_name = {"NOT AVAILABLE", "UNKNOW", "NO DISPONIBLE", "N A", "N/A"}

    def run(self):
        company_start, company_end, trade_start, trade_end = self.read_id_start_end()
        self.company_pipe(company_start, company_end)
        self.trade_pipe(trade_start, trade_end)

    def company_pipe(self, company_start, company_end, db='src_wmb'):
        if company_start >= company_end:
            logger.warning({"table": self.table_source_company, "msg": "未发现新数据"})
            return
        for i in range(company_start, company_end + 1, BATCH_SIZE):
            start = i
            end = i + BATCH_SIZE - 1
            if end > company_end:
                end = company_end
            data_raw = self.read_source_company(start, end, db)
            self.processing_company(data_raw)
            self.update_company_id(end, db)

    def trade_pipe(self, trade_start, trade_end):
        if trade_start >= trade_end:
            logger.warning({"table": self.table_source_trade, "msg": "未发现新数据"})
            return

        # 读取港口映射
        self.mapping_port = self.read_customs_port_map()
        # 单位映射
        self.mapping_quantity, self.mapping_weight = self.read_mapping_unit()

        for i in range(trade_start, trade_end + 1, BATCH_SIZE):
            start = i
            end = min(i + BATCH_SIZE - 1, trade_end)
            data_raw = self.read_source_trade(start, end, trade_end)
            self.processing_trade(data_raw)
            self.update_trade_id(end)

    def read_id_start_end(self, db='src_wmb'):
        table_record = f"{db}.{self.table_source_record}"
        table_company = f"{db}.{self.table_source_company}"
        # 读取trade的自增id
        sql = f"select company_id, trade_aid from {table_record}"
        # data = self.read_data(self.pool_source, sql)
        data = self.db_source.read(sql)
        company_start, trade_start = data[0]
        # 读取trade最大自增id
        sql = f"select max(aid) from {self.table_source_trade}"
        # data = self.read_data(self.pool_source, sql)
        data = self.db_source.read(sql)
        trade_end, = data[0]
        # 读取company最大自增id
        sql = f"select max(id) from {table_company}"
        # data = self.read_data(self.pool_source, sql)
        data = self.db_source.read(sql)
        company_end, = data[0]
        logger.info({
            "company_start": company_start, "company_end": company_end,
            "trade_start": trade_start, "trade_end": trade_end
        })
        return company_start, company_end, trade_start, trade_end

    def update_record_cache(self, db):
        table_record = f"{db}.{self.table_source_record}"
        table_company = f"{db}.{self.table_source_company}"
        sql = f"update {table_record} set company_id=(select min(id) from {table_company})"
        # self.execute_one(self.pool_source, sql)
        self.db_source.execute(sql)

    def read_id_start_end_cache_company(self, db='src_wmb'):
        table_record = f"{db}.{self.table_source_record}"
        table_company = f"{db}.{self.table_source_company}"
        # 读取trade的自增id
        sql = f"select company_id, trade_aid from {table_record}"
        # data = self.read_data(self.pool_source, sql)
        data = self.db_source.read(sql)
        company_start, trade_start = data[0]

        # 读取company最大自增id
        sql = f"select max(id) from {table_company}"
        # data = self.read_data(self.pool_source, sql)
        data = self.db_source.read(sql)
        company_end, = data[0]
        logger.info({
            "company_start": company_start, "company_end": company_end,
        })
        return company_start, company_end

    def read_source_company(self, start=1, end=1, db='src_wmb'):
        table_company = f"{db}.{self.table_source_company}"
        sql = self.read_company.format(table_company, start, end)
        logger.info({"table": table_company, "start": start, "end": end})
        # return self.read_data(self.pool_source, sql)
        return self.db_source.read(sql)

    def read_source_trade(self, start, end, max_id):
        sql = self.read_trade.format(self.table_source_trade, start, end)
        logger.info({"table": self.table_source_trade, "start": start, "end": end, "max_id": max_id})
        # return self.read_data(self.pool_source, sql)
        return self.db_source.read(sql)

    def processing_company(self, data_raw):
        if len(data_raw) == 0:
            return
        insert_list = list()
        cur_id_set = set()
        for data in data_raw:
            company_id, name, business, country, address, manager, telephone, fax, email, website = data
            name = self.transform_company_name(name)
            if name == '':  # 没有名字的公司没有意义
                continue
            openid = company_id  # 平台唯一key
            # 去重
            if openid in cur_id_set or self._exists_company(openid):
                continue
            introduce = ''  # text类型
            industry = ''
            scope = business  # text类型
            attach = ''  # text类型
            texture = ''  # text类型
            standard = self.mapping_country.get(country.lower(), dict())
            country_id = standard.get('id', 0)
            country = standard.get('name_en', '')
            # uniqueid = f"{openid}-{country_id}"
            uniqueid = f"{name}-{country_id}"  # 并不是唯一key
            phone = telephone

            # 处理邮箱
            email_new = self.extract_emails(email)
            phone_new = self.extract_phones(phone)

            item = [
                self.source, openid, uniqueid, name, introduce, industry, scope, attach, texture,
                country_id, country, address, phone_new, email_new, website
            ]
            insert_list.append(item)
            cur_id_set.add(openid)
        if len(insert_list) > 0:
            logger.info({"table": self.table_target_company, "len": len(insert_list)})
            # self.write_data(self.pool_target, self.insert_company, insert_list)
            self.db_target.write(self.insert_company, insert_list)
        if len(cur_id_set) > 0:
            self._add_set_company([[x, ] for x in cur_id_set])

    def processing_trade(self, data_raw):
        if len(data_raw) == 0:
            return
        insert_list = list()
        cur_id_set = set()
        uuid_list = list()

        # 读取当前最大trade_id，在代码中生成自增id
        max_trade_id = self.read_max_trade_id()
        cur_trade_id = max_trade_id + 1

        for data in data_raw:
            amount, bill_no, buyer, buyer_country, buyer_id_std, buyer_port, container, _date, descript, \
                descript_label, hs, _id, notify_name, origin_country, qty, qty_unit, seller, \
                seller_country, seller_id_std, seller_port, trans, weight, weight_unit, uusd = data
            # 源网站id
            openid = _id
            if openid == "":
                continue
            # 去重，因分表去重，故可以使用平台唯一key
            # if openid in cur_id_set or self._exists_trade(openid):
            #     continue
            # 结果表唯一key
            uniqueid = f"{self.source}-{openid}"
            # 提单号
            trade_code = bill_no
            # 时间戳
            d_shanghai = self.shanghai_tz.localize(_date)
            trade_date = int(d_shanghai.timestamp()) * 1000
            # 重量
            weight_res = 0
            if weight != '':
                try:
                    weight_res = round(abs(float(weight)))
                except ValueError:
                    weight_res = 0
            # 数量
            quantity = 0
            if qty != '':
                try:
                    quantity = round(abs(float(qty)))
                except ValueError:
                    quantity = 0
            # 数量单位
            quantity_unit = self.transform_unit(qty_unit, self.mapping_quantity)
            # 重量单位
            weight_unit = self.transform_unit(weight_unit, self.mapping_weight)
            # 总价
            amount_res = 0
            if amount != '':
                try:
                    amount_res = round(abs(float(amount)))
                except ValueError:
                    amount_res = 0
            if amount_res > 10000000000000:  # 过滤价格的异常值
                continue
            # 单价
            price = 0
            if uusd:
                try:
                    price = int(uusd)
                    print(uusd)
                except:
                    price = 0
            if not price:
                price = 0 if quantity == 0 else round(amount_res / quantity)
            # 价格单位
            amount_unit = ''  # wmb没有这个字段
            # 国家映射
            # 原产国
            product_info = self.mapping_country.get(origin_country.lower(), dict())
            product_country_id = product_info.get('id', 0)
            product_country = product_info.get('name_en', '')
            # 供应国
            seller_info = self.mapping_country.get(seller_country.lower(), dict())
            seller_country_id = seller_info.get('id', 0)
            seller_country = seller_info.get('name_en', '')
            # 采购国
            buyer_info = self.mapping_country.get(buyer_country.lower(), dict())
            buyer_country_id = buyer_info.get('id', 0)
            buyer_country = buyer_info.get('name_en', '')
            # 产品描述
            product_desc = descript
            # 产品标签
            product_tag = descript_label
            # 产品hscode
            product_hscode = self.transform_hs_code(hs)
            # 清洗公司名称
            seller = self.transform_company_name(seller)
            buyer = self.transform_company_name(buyer)
            # 源id
            seller_openid = seller_id_std
            if seller_openid == 0:
                seller_id = 0
            else:
                seller_id = self._query_company_id_by_openid(
                    seller_openid, company_name=seller, country_id=seller_country_id, country=seller_country, company_type=1)
            buyer_openid = buyer_id_std
            if buyer_openid == 0:
                buyer_id = 0
            else:
                buyer_id = self._query_company_id_by_openid(
                    buyer_openid, company_name=buyer, country_id=buyer_country_id, country=buyer_country, company_type=2)

            # 。。。
            notifier = notify_name
            # 中转
            transport = trans

            # 出口港
            seller_port = self.transform_port(seller_port)
            # 进口港
            buyer_port = self.transform_port(buyer_port)

            # 新版去重方案
            weight_res = int(float(weight_res))
            quantity = int(float(quantity))
            amount_res = int(float(amount_res))
            uuid_data = [
                trade_date, seller, seller_country_id, seller_port, buyer, buyer_country_id, buyer_port,
                product_desc, weight_res, quantity, amount_res
            ]  # 用date(贸易日期),seller(供应商),seller_country_id(供应国家id),出口港,采购商,采购商国家id(id),采购港,产品描述,重量,数量,总价
            uuid, cid = self.create_uuid_cid(uuid_data)

            if uuid in cur_id_set or self._exists_trade_uuid(uuid):
                continue
            uuid_list.append([cur_trade_id, uuid, cid])

            # ========== 过滤 ==========
            # 跳过供应商和采购商同时为空的情况
            # if seller_id == 0 and buyer_id == 0:
            #     continue
            # 产品描述、采购商、供应商、数量、数量单位、hscode全有，国家或港口至少有一对
            # if seller_id == 0 or buyer_id == 0 or product_desc == '' or \
            #         quantity == 0 or quantity_unit=='' or product_hscode=='':
            #     continue
            # if seller_id == 0 or buyer_id == 0 or product_desc == '':
            #     continue
            # elif (seller_country_id == 0 or buyer_country_id == 0) and (seller_port == '' or buyer_port == ''):
            #     continue

            item = [
                cur_trade_id, self.source, openid, uniqueid, trade_code, trade_date, weight_res, weight_unit,
                quantity, quantity_unit, price, amount_res, amount_unit, product_country_id, product_country,
                product_desc, product_tag, product_hscode, seller_id, seller_openid, seller, seller_country_id,
                seller_country, seller_port, buyer_id, buyer_openid, buyer, buyer_country_id, buyer_country,
                buyer_port, notifier, container, transport
            ]
            insert_list.append(item)
            cur_id_set.add(uuid)
            cur_trade_id += 1  # 自增id

        logger.info(f"all data process success")
        if len(insert_list) > 0:
            logger.info({"table": self.table_target_trade, "len": len(insert_list)})
            # self.write_data(self.pool_target, self.insert_trade, insert_list)
            self.db_target.write(self.insert_trade, insert_list)
        if len(uuid_list) > 0:
            self._save_trade_uuid(uuid_list)
        # if len(cur_id_set) > 0:
        #     self._add_set_trade([[i, ] for i in cur_id_set])

    def create_uuid_cid(self, data):
        """生成uuid和cid"""
        data_list = [str(d).strip().lower() for d in data]
        uuid = hashlib.md5(''.join(data_list).encode()).hexdigest()
        cid = hashlib.md5(''.join(data_list[:-3]).encode()).hexdigest()
        return uuid, cid

    def transform_company_name(self, name: str):
        if name == "":
            return name
        name = name.strip().upper()
        for black in self.black_name:
            if name.startswith(black):
                return ""
        return name

    def read_customs_port_map(self):
        sql = f"select port_raw, port_new from {self.table_port_map} where flag in (1, 2, 3)"
        data_raw = self.db_target.read(sql)
        if data_raw is None or len(data_raw) == 0 or data_raw[0] is None:
            raise ValueError("读取港口映射失败！")
        mapping = {port_raw.upper(): port_new.upper() for port_raw, port_new in data_raw}
        return mapping

    def read_mapping_unit(self):
        table_quantity = "haiguan_mapping_quantity_unit"
        table_weight = "haiguan_mapping_weight_unit"
        sql_quantity = f"select src, res from {table_quantity};"
        sql_weight = f"select src, res from {table_weight};"
        data_quantity = self.db_target.read(sql_quantity)
        data_weight = self.db_target.read(sql_weight)
        mapping_quantity = {k: v for k, v in data_quantity}
        mapping_weight = {k: v for k, v in data_weight}
        return mapping_quantity, mapping_weight

    def transform_port(self, raw):
        new_port = raw.strip().upper()
        if new_port == '-':
            new_port = ''
        elif new_port == 'N/A':
            new_port = ''
        elif new_port.isdigit():
            new_port = ''
        if len(new_port) > 100:
            new_port = ''
        if new_port != '':
            port = self.mapping_port.get(new_port)
            if port is not None:
                new_port = port
        return new_port

    def transform_unit(self, raw, mapping):
        src = raw.upper().strip()
        res = self.regex.sub('', raw)
        return '' if res == '' else mapping.get(res, src)

    def transform_hs_code(self, src):
        hs_code_list = self.regex_split_hs.split(src)
        res_list = [x.replace(".", "") for x in hs_code_list if "E+" not in x.upper()]
        return ', '.join(res_list)

    def _exists_company(self, openid):
        sql = f"select id from {self.table_exists_company} where uniqueid='{openid}'"
        # data_raw = self.read_data(self.pool_target, sql)
        data_raw = self.db_target.read(sql)
        return False if len(data_raw) == 0 else True

    def _add_set_company(self, id_list):
        sql = f"insert into {self.table_exists_company}(uniqueid) value(%s) on duplicate key update uniqueid=values(uniqueid)"
        logger.info({"table": self.table_exists_company, "length": len(id_list)})
        # self.write_data(self.pool_target, sql, id_list)
        self.db_target.write(sql, id_list)

    def update_company_id(self, _id, db='src_wmb'):
        table_record = f"{db}.{self.table_source_record}"
        sql = f"update {table_record} set company_id = {_id}"
        logger.info(sql)
        # self.execute_one(self.pool_source, sql)
        self.db_source.execute(sql)

    def read_max_trade_id(self):
        """读取最大的trade_id"""
        sql = f"select max(trade_id) from {self.table_target_trade}"
        result = self.db_target.read(sql)
        if result is None or len(result) == 0:
            raise ValueError('读取max trade_id失败！！！')
        return int(result[0][0])

    def _exists_trade(self, openid):
        sql = f"select id from {self.table_exists_trade} where openid = '{openid}'"
        # data_raw = self.read_data(self.pool_target, sql)
        data_raw = self.db_target.read(sql)
        return False if len(data_raw) == 0 else True

    def _exists_trade_uuid(self, uuid):
        """检测uuid是否已经存在"""
        sql = f"select trade_id from {self.table_trade_uuid} where uuid={uuid!r} limit 1"
        # data_raw = self.read_data(self.pool_target, sql)
        data_raw = self.db_target.read(sql)
        return False if len(data_raw) == 0 else True

    def _save_trade_uuid(self, data_list):
        sql = f"insert ignore into {self.table_trade_uuid}(trade_id, uuid, cid) values (%s,%s,%s)"
        self.db_target.write(sql, data_list)

    def _query_company_id_by_openid(self, openid, company_name, country_id=None, country=None, company_type=None):
        sql = f"select company_id from {self.table_target_company} where openid='{openid}' and source={self.source}"
        # data_raw = self.read_data(self.pool_target, sql)
        data_raw = self.db_target.read(sql)
        if data_raw is None or len(data_raw) == 0:
            uniqueid = f"{openid}-{country_id}"
            insert_sql = f"""insert into {self.table_target_company} (source, openid, uniqueid, name, country_id, country, company_type, introduce, industry, scope, attach, texture) 
            values ({self.source}, '{openid}', '{uniqueid}', '{company_name}', '{country_id}', '{country}', '{company_type}', '', '', '', '', '')"""
            self.db_target.execute(insert_sql)
            data_raw = self.db_target.read(sql)
            if data_raw:
                # 如果数据存储成功，在去重表也应该添加，防止出现重复数据
                self._add_set_company([[openid]])
        return 0 if data_raw is None or len(data_raw) == 0 else data_raw[0][0]

    def _add_set_trade(self, id_list):
        sql = f"insert into {self.table_exists_trade}(openid) value(%s) on duplicate key update openid=values(openid)"
        logger.info({"table": self.table_exists_trade, "length": len(id_list)})
        # self.write_data(self.pool_target, sql, id_list)
        self.db_target.write(sql, id_list)

    def update_trade_id(self, _id):
        sql = f"update t_routine_config set trade_aid = {_id}"
        logger.info(sql)
        # self.execute_one(self.pool_source, sql)
        self.db_source.execute(sql)

    def _mapping_country(self):
        # 读取c_area_country到内存
        sql = "select id, name_en, name_alias, code from db_customs.c_area_country"
        # data_raw = self.read_data(self.pool_target, sql)
        data_raw = self.db_target.read(sql)
        mapping = dict()
        for data in data_raw:
            _id, name_en, name_alias, code = data
            standard = {"id": _id, "name_en": name_en}
            mapping[name_en.lower()] = standard
            if len(name_alias) > 0 and name_alias.startswith('['):
                for a in json.loads(name_alias):
                    mapping[a.lower()] = standard
            mapping[code.lower()] = standard
        return mapping

    def extract_emails(self, text: str):
        res = self.regex_email.findall(text)
        return ','.join(res) if len(res) > 0 else ''

    def extract_phones(self, text: str):
        res = self.regex_phone.findall(text)
        return ','.join(res) if len(res) > 0 else ''

    @property
    def read_trade(self):
        fields = [
            'amount', 'bill_no', 'buyer', 'buyer_country', 'buyer_id_std', 'buyer_port', 'container', 'date',
            'descript', 'descript_label', 'hs', 'id', 'notify_name', 'origin_country', 'qty', 'qty_unit',
            'seller', 'seller_country', 'seller_id_std', 'seller_port', 'trans', 'weight', 'weight_unit',
            'uusd'
        ]
        read_trade = f"select {', '.join(fields)} from " + "{} where aid between {} and {}"
        return read_trade

    @property
    def insert_trade(self):
        fields_trade = [
            'trade_id', 'source', 'openid', 'uniqueid', 'trade_code', 'trade_date', 'weight', 'weight_unit',
            'quantity', 'quantity_unit', 'price', 'amount', 'amount_unit', 'product_country_id', 'product_country',
            'product_desc', 'product_tag', 'product_hscode', 'seller_id', 'seller_openid', 'seller',
            'seller_country_id', 'seller_country', 'seller_port', 'buyer_id', 'buyer_openid', 'buyer',
            'buyer_country_id', 'buyer_country', 'buyer_port', 'notifier', 'container', 'transport'
        ]
        update_str_trade = ', '.join([f"{f}=values({f})" for f in fields_trade])
        insert_trade = f"""insert into {self.table_target_trade}({', '.join(fields_trade)}) 
            value({', '.join(['%s'] * len(fields_trade))}) 
            on duplicate key update {update_str_trade}"""
        return insert_trade

    @property
    def read_company(self):
        fields_company_read = [
            'company_id', 'name', 'business', 'country', 'address', 'manager',
            'telephone', 'fax', 'email', 'website'
        ]
        read_company = f"select {', '.join(fields_company_read)} from " + "{} where id between {} and {}"
        return read_company

    @property
    def insert_company(self):
        fields_company = [
            'source', 'openid', 'uniqueid', 'name', 'introduce', 'industry', 'scope', 'attach', 'texture',
            'country_id', 'country', 'address', 'phone', 'email', 'website'
        ]
        insert_company = f"insert into {self.table_target_company}({', '.join(fields_company)}) " \
                         f"value({', '.join(['%s'] * len(fields_company))})"
        return insert_company

    def test_connect(self):
        sql = "show tables;"
        res = self.db_source.read(sql)
        print(res)


"""
"config_id"	"company_id"	"company_limit_id"	"trade_aid"	"trade_limit_aid"
"1"	         "14080080"	            "0"	        "*********"	       "0"
*********
*********
"""


@statistics()
def main():
    # test
    # w = WMB()
    # w.run()
    # line
    try:
        w = WMB()
        w.run()
        send_text_message(f"外贸邦数据处理完成")
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        msg = f"{exc_type.__name__}: {e}"
        logger.warning(msg)
        send_text_message(f"外贸邦数据处理异常中止：{msg}")


def test():
    p = WMB()
    s_t = time.time()
    # 读取港口映射
    p.mapping_port = p.read_customs_port_map()
    e_t = time.time()
    print(f'读取港口映射完成；耗时：{e_t - s_t}s')
    s_t = time.time()
    # 单位映射
    p.mapping_quantity, p.mapping_weight = p.read_mapping_unit()
    e_t = time.time()
    print(f'单位映射完成；耗时：{e_t - s_t}s')

    # 贸易数据 同步
    fields_trade_read = [
        'amount', 'bill_no', 'buyer', 'buyer_country', 'buyer_id_std', 'buyer_port', 'container', 'date',
        'descript', 'descript_label', 'hs', 'id', 'notify_name', 'origin_country', 'qty', 'qty_unit',
        'seller', 'seller_country', 'seller_id_std', 'seller_port', 'trans', 'weight', 'weight_unit',
        'uusd'
    ]
    sql_trade = f"select {', '.join(fields_trade_read)} from src_wmb.trade where aid between 7670545102 and 7670545102"
    # sql_trade = f'''
    #     select {', '.join(fields_trade_read)} from src_wmb.trade where update_time between "2025-09-05 11:35:46" and "2025-09-05 11:35:46"
    # '''
    trade_data_raw = p.db_source.read(sql_trade)

    # 找到 所有 公司 ID
    company_ids = set()
    for trade in trade_data_raw:
        print(trade)
        company_ids.add(trade[18])
        company_ids.add(trade[4])
    if not company_ids:
        return
    company_ids_str = ','.join([str(i) for i in company_ids])
    # 获取 公司数据 同步
    fields_company_read = [
        'company_id', 'name', 'business', 'country', 'address', 'manager',
        'telephone', 'fax', 'email', 'website'
    ]
    company_sql = f"select {', '.join(fields_company_read)} from src_wmb.company where company_id in ({company_ids_str})"
    company_data_raw = p.db_source.read(company_sql)
    # print(company_data_raw)

    # 同步相关公司信息入 t_company 表
    p.processing_company(company_data_raw)

    # 同步相关贸易信息入 t_trade 表
    p.processing_trade(trade_data_raw)



        # p.update_trade_id(end)


"""
cd /root/peng_file/data_processing/wmb && nohup python3 -u wmb_data_process.py > ./logs/wmb_data_process.log 2>&1 &
tail logs/wmb_data_process.log -f
kill $(ps -ef | grep wmb_data_process.py | grep -v grep | awk '{print $2}')
"""

if __name__ == '__main__':
    main()
    # WMB().test_connect()

    # test()
