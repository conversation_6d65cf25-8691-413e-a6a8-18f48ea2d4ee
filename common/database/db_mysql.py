#!/usr/bin/env python3
# encoding: utf8
# name: 高效操作数据库
# auth: peng
# email: la<PERSON>hulu<PERSON>@gmail.com
# updated: 2023-07-07

import pymysql
import asyncio
from common.utils.color import Color
from common.utils.bar import Bar
from typing import Union, List, Tuple
from dbutils.pooled_db import PooledDB
from setting import *


class DBMysql:
    def __init__(self, conn, **kwargs):
        self.name = kwargs.get('name', 'mysql')
        self.conn = conn
        self.c = Color()

    def execute(self, sql: str, retry_num: int = 3) -> Union[int, None]:
        for _ in range(retry_num):
            try:
                self.conn.ping(reconnect=True)
                with self.conn.cursor() as cur:
                    cur.execute(sql)
                self.conn.commit()
                return True
            except Exception as e:
                self.conn.rollback()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                logger.warning(f"{exc_type.__name__}: {e}")
        logger.error({"maximum_retry": retry_num, "sql": sql})

    def read(self, sql: str, retry_num: int = 3) -> Union[tuple, None]:
        for _ in range(retry_num):
            try:
                self.conn.ping(reconnect=True)
                with self.conn.cursor() as cur:
                    cur.execute(sql)
                    data_raw = cur.fetchall()
                return tuple() if data_raw is None else data_raw
            except Exception as e:
                exc_type, exc_obj, exc_tb = sys.exc_info()
                logger.warning(f"{exc_type.__name__}: {e}")
        logger.error({"msg": "最大重试次数", "sql": sql})

    def write(
            self, sql: str,
            insert_list: Union[List[List], Tuple[Tuple], List[Tuple], Tuple[List]],
            retry_num: int = 3
    ) -> Union[bool, None]:
        for _ in range(retry_num):
            self.conn.ping(reconnect=True)
            try:
                with self.conn.cursor() as cur:
                    cur.executemany(sql, insert_list)
                self.conn.ping(reconnect=True)
                self.conn.commit()
                return True
            except Exception as e:
                self.conn.rollback()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                logger.warning(f"{exc_type.__name__}: {e}")
        logger.error({"maximum_retry": retry_num, "sql": sql})

    def write_bar(
            self, sql: str,
            insert_list: Union[List[List], Tuple[Tuple], List[Tuple], Tuple[List]],
            retry_num: int = 3, name: str = "mysql"
    ):
        """
        Batch insert/update/upsert
        template:
            INSERT INTO table VALUE(%s, %s)  [[1, 'zs'], [2, 'ls']]
            UPDATE table SET key=%s WHERE id=%s
            INSERT INTO table(id, name) VALUE(%s, %s) ON DUPLICATE KEY UPDATE name=VALUES(name)
        :param retry_num: 重试次数
        :param name: 进度条显示的名字
        :param sql: str
        :param insert_list: list(list())
        :return:
        """
        total = len(insert_list)
        if not total:
            print(self.c.pure("无更新数据", color=34))
            return 1
        step = 1000 if total >= 1000 else total
        bar = Bar(name=self.name if name == '' else name)
        for i in range(0, total, step):
            j = i + step
            for _ in range(retry_num):
                self.conn.ping(reconnect=True)
                try:
                    with self.conn.cursor() as cur:
                        cur.executemany(sql, insert_list[i: j])
                    self.conn.ping(reconnect=True)
                    self.conn.commit()
                    break
                except Exception as e:
                    self.conn.rollback()
                    exc_type, exc_obj, exc_tb = sys.exc_info()
                    logger.warning(f"{exc_type.__name__}: {e}")
            logger.error({"maximum_retry": retry_num, "sql": sql})
            k = j if j <= total else total
            bar.run(k, total)
        return 1

    def close(self):
        self.conn.close()


class PoolMysql:
    def __new__(cls, concurrency: int = 1, **mysql_info):
        max_conn = 20 if concurrency < 20 else concurrency
        info = dict(
            blocking=True,  # 连接池中如果没有可用连接后，是否阻塞等待
            ping=1,  # 在每次获取连接时ping
            mincached=5,  # 初始化时，连接池中至少创建的空闲的连接，0表示不创建
            maxcached=10,  # 连接池中最多闲置的连接，0和None不限制
            maxconnections=max_conn,  # 连接池允许的最大连接数，0和None表示不限制连接数
        )
        info.update(mysql_info)
        return PooledDB(creator=pymysql, **info)


class DBPoolMysql:
    def __init__(self, pool: PooledDB):
        self.pool = pool

    def execute(self, sql: str, retry_num: int = 3) -> Union[int, None]:
        for _ in range(retry_num):
            conn = self.pool.connection()
            cursor = conn.cursor()
            try:
                cursor.execute(sql)
                conn.commit()
                return True
            except Exception as e:
                conn.rollback()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                logger.warning(f"{exc_type.__name__}: {e}")
            finally:
                cursor.close()
                conn.close()  # 将连接归还给连接池
        logger.error({"msg": "最大重试次数", "sql": sql})

    def read(self, sql: str, retry_num: int = 3) -> Union[tuple, None]:
        for _ in range(retry_num):
            conn = self.pool.connection()
            cursor = conn.cursor()
            try:
                cursor.execute(sql)
                data_raw = cursor.fetchall()
                return data_raw if data_raw else tuple()
            except Exception as e:
                exc_type, exc_obj, exc_tb = sys.exc_info()
                logger.warning(f"{exc_type.__name__}: {e}")
            finally:
                cursor.close()
                conn.close()  # 将连接归还给连接池
        logger.error({"msg": "最大重试次数", "sql": sql})

    def write(
            self, sql: str,
            insert_list: Union[List[List], Tuple[Tuple], List[Tuple], Tuple[List]],
            retry_num: int = 3
    ) -> Union[bool, None]:
        for _ in range(retry_num):
            conn = self.pool.connection()
            cursor = conn.cursor()
            try:
                cursor.executemany(sql, insert_list)
                conn.commit()
                return True
            except Exception as e:
                conn.rollback()
                exc_type, exc_obj, exc_tb = sys.exc_info()
                logger.warning(f"{exc_type.__name__}: {e}")
            finally:
                cursor.close()
                conn.close()  # 将连接归还给连接池
        logger.error({"msg": "最大重试次数", "sql": sql})

    def close(self):
        self.pool.close()


class AsyncDBPoolMysql:
    def __init__(self, pool: PooledDB):
        self.pool = pool
        self.__db_pool = DBPoolMysql(self.pool)

    async def execute(self, sql: str, retry_num: int = 3) -> Union[int, None]:
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.__db_pool.execute, sql, retry_num)

    async def read(self, sql: str, retry_num: int = 3) -> Union[tuple, None]:
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.__db_pool.read, sql, retry_num)

    async def write(self, sql: str, insert_list: List[List], retry_num: int = 3) -> Union[int, None]:
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.__db_pool.write, sql, insert_list, retry_num)

    async def close(self):
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self.__db_pool.close)
