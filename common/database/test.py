import redis
from rq import Queue
from common.database.db_redis import PriorityQueue
redis_url = "redis://:test@127.0.0.1:6397/0"


class Test:
    """
    test
    """

    def __get__(self, instance, owner):
        pass

    def __set__(self, instance, value):
        pass


if __name__ == '__main__':
    # 创建 Redis 连接
    # redis_conn = redis.StrictRedis.from_url(redis_url, decode_responses=True)
    #
    # # 创建优先队列实例
    # queue = PriorityQueue("my_queue", redis_conn)
    #
    # # 入队操作，包含去重校验
    # # queue.push({"key": 1, "score": 1, "retry_num": 0})
    # # queue.push({"key": 2, "score": 2, "retry_num": 0})
    # # queue.push({"key": 3, "score": 3, "retry_num": 0})
    # # queue.push({"key": 4, "score": 4, "retry_num": 0})
    #
    # # 批量出队操作
    # batch_size = 2
    # items = queue.pull(batch_size, desc=True)
    # print("Dequeued:", items)

    t = Test()
