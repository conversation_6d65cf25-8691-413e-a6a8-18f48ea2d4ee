#!/usr/bin/env python3
# encoding: utf8
# name: 进度条
# auth: peng
# updated: 2023-04-17

from common.utils.color import Color


class Bar:
    def __init__(self, **kwargs):
        """
        进度条
        :param kwargs:
            :param w: 进度条宽度
            :param name: 进度条左侧的标识
            :param raw: True：以纯色显示，False：以彩色颜色
        """
        self.kwargs = kwargs
        self.w = kwargs.get('w', 20)
        self.name = kwargs.get('name', 'task')
        self.raw = kwargs.get('raw', False)
        self.color = Color()

    def run(self, num, total, cur=''):
        cur = f" | {cur}" if cur else ''
        r = num / total
        head = self.color.pure(f"{self.name} [{int(r * 100)}%] [")
        tail = self.color.pure(f"] {num}/{total}{cur}")
        if self.raw:
            strip = self.color.pure((('=' * int(r * self.w)) + '>').ljust(self.w))
        else:
            strip = self.color.next((('=' * int(r * self.w)) + '>').ljust(self.w))
        print(f"\r{head}{strip}{tail}", end='', flush=True)
        if num == total:
            print()
