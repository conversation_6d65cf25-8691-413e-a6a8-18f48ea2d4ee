#!/usr/bin/env python3
# encoding: utf8
# name: 文字炫彩皮肤
# auth: peng
# updated: 2023-04-17


import time
from random import choice
from colorama import init
init()


class Color:
    colors = list(range(30, 39))

    def __init__(self, **kwargs):
        self.offset = 0
        self.color = kwargs.get('color', 36)
        self.order = kwargs.get('order', False)

    def pure(self, string, **kwargs):
        pure_color = kwargs.get('color', self.color)
        if 30 <= pure_color < 39:
            return f"\033[0;{pure_color};50m{string}\033[0m"
        else:
            raise ValueError("The pure color code range is from 30 to 38.")

    def chaos(self, string):
        res = ''
        string = str(string)
        for i in range(0, len(string)):
            res += f"\033[0;{choice(self.colors)};50m{string[i]}"
        res += '\033[0m'
        return res

    @staticmethod
    def colored(string):
        res = ''
        string = str(string)
        width = len(string)
        chunk = list(range(30, 39)) * (int(width / 9) + 1)
        for w in range(width):
            res += f"\033[0;{chunk[w]};50m{string[w]}"
        res += '\033[0m'
        return res

    def __move__(self, string, to_right=True, step=1):
        res = ''
        string = str(string)
        width = len(string)
        chunk = list(range(30, 39)) * (int(width / 9) + 1)
        self.offset += step
        self.offset %= len(self.colors)
        re_offset = -1 - self.offset if to_right else self.offset
        head, tail = chunk[:re_offset], chunk[re_offset:]
        chunk = tail + head
        for w in range(width):
            res += f"\033[0;{chunk[w]};50m{string[w]}"
        res += '\033[0m'
        return res

    def next(self, string, step=1):
        return self.__move__(string, to_right=True, step=step)

    def last(self, string, step=1):
        return self.__move__(string, to_right=False, step=step)

    @staticmethod
    def help():
        c = Color()
        other = "这是一段为了展示效果而写的文本"
        for i in range(30, 39):
            print(c.pure(f'color - {i}', color=i))
        print(c.chaos(f'随机彩色 - {other}'))
        print(c.colored(f'有序彩色 - {other}'))
        c1 = Color()
        for i in range(2000):
            content = " - 有序动态彩色 - "
            print(f"\r{c.next(other)}{c.pure(content)}{c1.last(other)}", end='')
            time.sleep(.1)
        print()


if __name__ == '__main__':
    Color.help()
