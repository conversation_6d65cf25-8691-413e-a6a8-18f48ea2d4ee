import json
from concurrent.futures import ThreadPoolExecutor, as_completed
from common.database import PoolMysql, DBPoolMysql
from common.utils import statistics
from setting import *


class Cache:
    def __init__(self, concurrency=1):
        self.pool_target = PoolMysql(**MYSQL_INFO_TARGET)
        self.db_mysql = DBPoolMysql(self.pool_target)
        self.concurrency = concurrency
        self.table_company = 't_company'
        self.table_trade = 't_trade'
        self.batch_size = 1000

    def run(self):
        self.update_trade_by_company()
        # self.update_trade_by_json()

    def update_trade_by_company(self):
        min_id, max_id = self.read_id_range()
        # min_id = 44816001
        with ThreadPoolExecutor(max_workers=self.concurrency) as executor:
            tasks = []
            for i in range(min_id, max_id + 1, self.batch_size):
                start = i
                end = i + self.batch_size - 1
                if end > max_id:
                    end = max_id
                tasks.append(executor.submit(self.company_id_pipe, start, end))
                # break
            for k, res in enumerate(as_completed(tasks), 1):
                try:
                    result = res.result()
                    # 处理结果（如果有需要的话）
                except Exception as e:
                    exc_type, exc_obj, exc_tb = sys.exc_info()
                    msg = f"{exc_type.__name__}: {e}"
                    logger.error(msg)

    def update_trade_by_json(self):
        # save local
        # with open('db_customs3.json', 'r', encoding='utf8') as f:
        #     data_raw = json.loads(f.read())
        # trade_id_list = [d.get('trade_id') for d in data_raw]
        # trade_id_list = [x for x in trade_id_list if isinstance(x, int) and x != 0]
        #
        # with open('trade_id_list.json', 'w', encoding='utf8') as f:
        #     f.write(json.dumps(trade_id_list))

        # update server
        with open('trade_id_list.json', 'r', encoding='utf8') as f:
            trade_id_list = json.loads(f.read())

        logger.info({"trade_id_list_length": len(trade_id_list)})
        with ThreadPoolExecutor(max_workers=self.concurrency) as executor:
            tasks = []
            for i in range(0, len(trade_id_list), self.batch_size):
                # self.overwriting_buyer_id(trade_id_list, i, self.batch_size)
                tasks.append(executor.submit(self.overwriting_buyer_id, trade_id_list, i, self.batch_size))
            for k, res in enumerate(as_completed(tasks), 1):
                try:
                    result = res.result()
                    # 处理结果（如果有需要的话）
                except Exception as e:
                    exc_type, exc_obj, exc_tb = sys.exc_info()
                    msg = f"{exc_type.__name__}: {e}"
                    logger.error(msg)

    def company_id_pipe(self, start, end):
        logger.info({"start": start, "end": end})
        data_raw = self.read_company_id(start, end)
        if data_raw is not None and len(data_raw) > 0:
            self.overwriting_company_id(data_raw)

    def overwriting_company_id(self, data_raw):
        update_list = [[c, o] for c, o in data_raw if o not in ('', '0')]
        logger.info({"write": "seller_id", "length": len(update_list)})
        sql = f"update {self.table_trade} set seller_id=%s where source=1 and seller_openid=%s"
        self.db_mysql.write(sql, update_list)

        logger.info({"write": "buyer_id", "length": len(update_list)})
        sql = f"update {self.table_trade} set buyer_id=%s where source=1 and buyer_openid=%s"
        self.db_mysql.write(sql, update_list)

    def overwriting_buyer_id(self, trade_id_list, i, batch_size):
        update_list = trade_id_list[i:i + batch_size]
        logger.info({"write": "buyer_id", "length": len(update_list), "i": i, "batch_size": batch_size})
        sql = f"update {self.table_trade} set buyer_id=0 where trade_id=%s"
        self.db_mysql.write(sql, update_list)

    def read_id_range(self, source=1):
        sql = f"select min(company_id) min_id, max(company_id) max_id from {self.table_company} " \
              f"where source = {source} and flag=0"
        data_raw = self.db_mysql.read(sql)
        min_id, max_id = data_raw[0]
        logger.info({"min_id": min_id, "max_id": max_id})
        return min_id, max_id

    def read_company_id(self, start, end, source=1):
        sql = f"select company_id, openid from {self.table_company} " \
              f"where source = {source} and company_id between {start} and {end} and flag = 0"
        data_raw = self.db_mysql.read(sql)
        logger.info({"length": len(data_raw)})
        return data_raw


@statistics()
def ignition():
    c = Cache(3)
    c.run()


"""
cd /root/peng_file/data_processing/wmb && nohup python3 -u cache_haiguan.py > ./logs/cache_haiguan.log 2>&1 &
tail logs/cache_haiguan.log -f
kill -9 $(ps -ef | grep cache_haiguan.py | grep -v grep | awk '{print $2}')
"""

if __name__ == '__main__':
    ignition()
