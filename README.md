# wmb数据处理

### 构建
```shell script
docker build -t data_process_wmb:v1 .
```
### 启动
```shell script
# 与宿主机共用网络，将日志文件夹挂载到./logs
docker run -d --name data_process_wmb_v1 --network host -v ./logs:/app/logs --log-opt max-size=10m --log-opt max-file=3 data_process_wmb:v1
```

### 运维
##### 查看容器实例信息
```shell script
docker ps -a | grep data_processing_dingyi_v1
```
##### 查看实时日志（或最后100条）
```shell script
docker logs $(docker ps -a | grep data_processing_dingyi_v1 | awk '{print $1}') -n 100 -f
docker logs $(docker ps -a | grep data_processing_dingyi_v1 | awk '{print $1}') | grep -E "WARNING|ERROR"
docker logs $(docker ps -a | grep data_processing_dingyi_v1 | awk '{print $1}') | grep -E "WARNING|ERROR" > ./logs/error.log
```
##### 将当前目录copy到项目工作目录
```shell script
docker cp ./ $(docker ps -a | grep data_processing_dingyi_v1 | awk '{print $1}'):/app/
```
##### 重启容器实例
```shell script
docker ps -a | grep data_processing_dingyi_v1 | awk '{print $1}' | xargs docker restart
```
##### 交互方式进入容易内部（需要正在运行的容器）
```shell script
docker exec -it $(docker ps | grep data_processing_dingyi_v1 | awk '{print $1}') /bin/bash
```
##### 删除容器实例
```shell script
docker ps -a | grep data_processing_dingyi_v1 | awk '{print $1}' | xargs docker rm -f
```